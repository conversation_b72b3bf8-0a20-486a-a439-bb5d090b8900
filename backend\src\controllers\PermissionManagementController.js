const db = require('../config/database');

/**
 * Permission Management Controller
 * Handles all permission-related operations for the UI
 */

// Get complete modules tree with submodules and permissions
exports.getModulesTree = async (req, res) => {
  console.log('🔍 Getting modules tree...');
  
  try {
    const query = `
      SELECT 
        m.Id as ModuleId,
        m.Name as ModuleName,
        m.Description as ModuleDescription,
        m.Icon as ModuleIcon,
        m.DisplayOrder as ModuleDisplayOrder,
        m.IsActive as ModuleIsActive,
        
        sm.Id as SubModuleId,
        sm.Name as SubModuleName,
        sm.Path as SubModuleRoute,
        sm.Icon as SubModuleIcon,
        sm.IsActive as SubModuleIsActive,
        
        p.Id as PermissionId,
        p.Name as PermissionName,
        p.Description as PermissionDescription,
        
        smp.Id as SubModulePermissionId
        
      FROM Modules m
      LEFT JOIN SubModules sm ON m.Id = sm.ModuleId AND sm.IsActive = 1
      LEFT JOIN SubModulePermissions smp ON sm.Id = smp.SubModuleId AND smp.IsActive = 1
      LEFT JOIN Permissions p ON smp.PermissionId = p.Id AND p.IsActive = 1
      WHERE m.IsActive = 1
      ORDER BY m.DisplayOrder, sm.Id, p.Name
    `;

    const result = await db.query(query);
    console.log(`✅ Query returned ${result.recordset.length} rows`);

    // Transform flat result into hierarchical structure
    const modulesMap = new Map();

    result.recordset.forEach(row => {
      // Create module if not exists
      if (!modulesMap.has(row.ModuleId)) {
        modulesMap.set(row.ModuleId, {
          id: row.ModuleId,
          name: row.ModuleName,
          description: row.ModuleDescription,
          icon: row.ModuleIcon,
          displayOrder: row.ModuleDisplayOrder,
          isActive: row.ModuleIsActive,
          subModules: new Map()
        });
      }

      const module = modulesMap.get(row.ModuleId);

      // Create submodule if not exists and if SubModuleId is not null
      if (row.SubModuleId && !module.subModules.has(row.SubModuleId)) {
        module.subModules.set(row.SubModuleId, {
          id: row.SubModuleId,
          name: row.SubModuleName,
          description: row.SubModuleName,
          route: row.SubModuleRoute,
          icon: row.SubModuleIcon,
          displayOrder: row.SubModuleId,
          isActive: row.SubModuleIsActive,
          permissions: []
        });
      }

      // Add permission if exists
      if (row.PermissionId && row.SubModuleId) {
        const subModule = module.subModules.get(row.SubModuleId);
        if (subModule && !subModule.permissions.find(p => p.id === row.PermissionId)) {
          subModule.permissions.push({
            id: row.PermissionId,
            name: row.PermissionName,
            description: row.PermissionDescription,
            subModulePermissionId: row.SubModulePermissionId
          });
        }
      }
    });

    // Convert Maps to Arrays for JSON response
    const modules = Array.from(modulesMap.values()).map(module => ({
      ...module,
      subModules: Array.from(module.subModules.values())
    }));

    console.log(`✅ Transformed into ${modules.length} modules`);

    res.json({
      success: true,
      data: modules,
      message: 'Modules tree retrieved successfully'
    });

  } catch (error) {
    console.error('❌ Error in getModulesTree:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};

// Get all roles
exports.getRoles = async (req, res) => {
  console.log('🔍 Getting all roles...');
  
  try {
    const query = `
      SELECT 
        Id,
        Name,
        IsActive,
        CreatedOn
      FROM Roles
      WHERE IsActive = 1
      ORDER BY Name
    `;

    const result = await db.query(query);
    console.log(`✅ Found ${result.recordset.length} roles`);

    res.json({
      success: true,
      data: result.recordset,
      message: 'Roles retrieved successfully'
    });

  } catch (error) {
    console.error('❌ Error in getRoles:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};

// Get permissions for a specific role
exports.getRolePermissions = async (req, res) => {
  const { roleId } = req.params;
  console.log(`🔍 Getting permissions for role ID: ${roleId}`);

  try {
    const query = `
      SELECT 
        rp.Id as RolePermissionId,
        rp.RoleId,
        rp.SubModulePermissionId,
        smp.SubModuleId,
        smp.PermissionId,
        sm.Name as SubModuleName,
        sm.Path as SubModuleRoute,
        p.Name as PermissionName,
        p.Description as PermissionDescription,
        m.Id as ModuleId,
        m.Name as ModuleName
      FROM RolePermissions rp
      INNER JOIN SubModulePermissions smp ON rp.SubModulePermissionId = smp.Id
      INNER JOIN SubModules sm ON smp.SubModuleId = sm.Id
      INNER JOIN Permissions p ON smp.PermissionId = p.Id
      INNER JOIN Modules m ON sm.ModuleId = m.Id
      WHERE rp.RoleId = @roleId
        AND rp.IsActive = 1
        AND smp.IsActive = 1
        AND sm.IsActive = 1
        AND p.IsActive = 1
        AND m.IsActive = 1
      ORDER BY m.DisplayOrder, sm.Id, p.Name
    `;

    const result = await db.query(query, { roleId });
    console.log(`✅ Found ${result.recordset.length} permissions for role`);

    res.json({
      success: true,
      data: result.recordset,
      message: 'Role permissions retrieved successfully'
    });

  } catch (error) {
    console.error('❌ Error in getRolePermissions:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};

// Update role permissions
exports.updateRolePermissions = async (req, res) => {
  const { roleId } = req.params;
  const { permissions } = req.body; // Array of subModulePermissionIds
  
  console.log(`🔍 Updating permissions for role ID: ${roleId}`);
  console.log(`📝 New permissions: ${permissions?.length || 0} items`);

  try {
    // Start transaction
    const transaction = await db.beginTransaction();

    try {
      const userId = req.user?.id || 1;
      
      // First, deactivate all existing permissions for this role
      const request1 = transaction.request();
      request1.input('roleId', roleId);
      request1.input('userId', userId);
      await request1.query(`
        UPDATE RolePermissions 
        SET IsActive = 0, 
            ModifiedOn = GETDATE(),
            ModifiedBy = @userId
        WHERE RoleId = @roleId
      `);

      // Then, add new permissions
      if (permissions && permissions.length > 0) {
        for (const subModulePermissionId of permissions) {
          // Check if permission already exists (might be deactivated)
          const request2 = transaction.request();
          request2.input('roleId', roleId);
          request2.input('subModulePermissionId', subModulePermissionId);
          const existingResult = await request2.query(`
            SELECT Id FROM RolePermissions 
            WHERE RoleId = @roleId AND SubModulePermissionId = @subModulePermissionId
          `);

          if (existingResult.recordset.length > 0) {
            // Reactivate existing permission
            const request3 = transaction.request();
            request3.input('roleId', roleId);
            request3.input('subModulePermissionId', subModulePermissionId);
            request3.input('userId', userId);
            await request3.query(`
              UPDATE RolePermissions 
              SET IsActive = 1, 
                  ModifiedOn = GETDATE(),
                  ModifiedBy = @userId
              WHERE RoleId = @roleId AND SubModulePermissionId = @subModulePermissionId
            `);
          } else {
            // Create new permission
            const request4 = transaction.request();
            request4.input('roleId', roleId);
            request4.input('subModulePermissionId', subModulePermissionId);
            request4.input('userId', userId);
            await request4.query(`
              INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedOn, CreatedBy)
              VALUES (@roleId, @subModulePermissionId, 1, GETDATE(), @userId)
            `);
          }
        }
      }

      // Commit transaction
      await transaction.commit();
      console.log('✅ Role permissions updated successfully');

      res.json({
        success: true,
        message: 'Role permissions updated successfully'
      });

    } catch (error) {
      // Rollback transaction on error
      await transaction.rollback();
      throw error;
    }

  } catch (error) {
    console.error('❌ Error in updateRolePermissions:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};

// Create new role
exports.createRole = async (req, res) => {
  const { name } = req.body;
  console.log(`🔍 Creating new role: ${name}`);

  try {
    // Check if role name already exists
    const existingRole = await db.query(`
      SELECT Id FROM Roles WHERE Name = @name AND IsActive = 1
    `, { name });

    if (existingRole.recordset.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Role already exists',
        message: 'A role with this name already exists'
      });
    }

    // Create new role
    const result = await db.query(`
      INSERT INTO Roles (Name, IsActive, CreatedOn, CreatedBy)
      OUTPUT INSERTED.Id, INSERTED.Name, INSERTED.IsActive, INSERTED.CreatedOn
      VALUES (@name, 1, GETDATE(), @userId)
    `, { 
      name, 
      userId: req.user?.id || 1 
    });

    console.log('✅ Role created successfully');

    res.json({
      success: true,
      data: result.recordset[0],
      message: 'Role created successfully'
    });

  } catch (error) {
    console.error('❌ Error in createRole:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};

// Update role
exports.updateRole = async (req, res) => {
  const { roleId } = req.params;
  const { name } = req.body;
  console.log(`🔍 Updating role ID: ${roleId}`);

  try {
    // Check if role exists
    const existingRole = await db.query(`
      SELECT Id FROM Roles WHERE Id = @roleId AND IsActive = 1
    `, { roleId });

    if (existingRole.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Role not found',
        message: 'Role not found or inactive'
      });
    }

    // Check if new name conflicts with existing role (excluding current role)
    if (name) {
      const nameConflict = await db.query(`
        SELECT Id FROM Roles 
        WHERE Name = @name AND Id != @roleId AND IsActive = 1
      `, { name, roleId });

      if (nameConflict.recordset.length > 0) {
        return res.status(400).json({
          success: false,
          error: 'Role name already exists',
          message: 'A role with this name already exists'
        });
      }
    }

    // Update role
    const result = await db.query(`
      UPDATE Roles 
      SET Name = COALESCE(@name, Name),
          ModifiedOn = GETDATE(),
          ModifiedBy = @userId
      OUTPUT INSERTED.Id, INSERTED.Name, INSERTED.IsActive, INSERTED.CreatedOn
      WHERE Id = @roleId
    `, { 
      roleId,
      name: name || null,
      userId: req.user?.id || 1 
    });

    console.log('✅ Role updated successfully');

    res.json({
      success: true,
      data: result.recordset[0],
      message: 'Role updated successfully'
    });

  } catch (error) {
    console.error('❌ Error in updateRole:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};

// Delete role (soft delete)
exports.deleteRole = async (req, res) => {
  const { roleId } = req.params;
  console.log(`🔍 Deleting role ID: ${roleId}`);

  try {
    // Check if role exists
    const existingRole = await db.query(`
      SELECT Id, Name FROM Roles WHERE Id = @roleId AND IsActive = 1
    `, { roleId });

    if (existingRole.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Role not found',
        message: 'Role not found or already inactive'
      });
    }

    // Check if role is assigned to any users
    const assignedUsers = await db.query(`
      SELECT COUNT(*) as UserCount FROM Users WHERE RoleId = @roleId AND IsActive = 1
    `, { roleId });

    if (assignedUsers.recordset[0].UserCount > 0) {
      return res.status(400).json({
        success: false,
        error: 'Role in use',
        message: 'Cannot delete role that is assigned to users'
      });
    }

    // Soft delete role
    await db.query(`
      UPDATE Roles 
      SET IsActive = 0,
          ModifiedOn = GETDATE(),
          ModifiedBy = @userId
      WHERE Id = @roleId
    `, { 
      roleId,
      userId: req.user?.id || 1 
    });

    // Also deactivate all role permissions
    await db.query(`
      UPDATE RolePermissions 
      SET IsActive = 0,
          ModifiedOn = GETDATE(),
          ModifiedBy = @userId
      WHERE RoleId = @roleId
    `, { 
      roleId,
      userId: req.user?.id || 1 
    });

    console.log('✅ Role deleted successfully');

    res.json({
      success: true,
      message: 'Role deleted successfully'
    });

  } catch (error) {
    console.error('❌ Error in deleteRole:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};

// Get permission matrix for all roles
exports.getPermissionMatrix = async (req, res) => {
  console.log('🔍 Getting permission matrix for all roles...');
  
  try {
    const query = `
      SELECT 
        r.Id as RoleId,
        r.Name as RoleName,
        m.Id as ModuleId,
        m.Name as ModuleName,
        m.DisplayOrder as ModuleDisplayOrder,
        sm.Id as SubModuleId,
        sm.Name as SubModuleName,
        sm.Path as SubModuleRoute,
        p.Id as PermissionId,
        p.Name as PermissionName,
        smp.Id as SubModulePermissionId,
        CASE WHEN rp.Id IS NOT NULL THEN 1 ELSE 0 END as HasPermission,
        rp.Id as RolePermissionId
        
      FROM Roles r
      CROSS JOIN Modules m
      CROSS JOIN SubModules sm
      CROSS JOIN Permissions p
      CROSS JOIN SubModulePermissions smp
      LEFT JOIN RolePermissions rp ON r.Id = rp.RoleId 
        AND smp.Id = rp.SubModulePermissionId 
        AND rp.IsActive = 1
        
      WHERE r.IsActive = 1
        AND m.IsActive = 1
        AND sm.IsActive = 1
        AND sm.ModuleId = m.Id
        AND p.IsActive = 1
        AND smp.SubModuleId = sm.Id
        AND smp.PermissionId = p.Id
        AND smp.IsActive = 1
        
      ORDER BY r.Name, m.DisplayOrder, sm.Id, p.Name
    `;

    const result = await db.query(query);
    console.log(`✅ Query returned ${result.recordset.length} matrix entries`);

    // Transform flat result into matrix array (frontend expects array)
    const matrixArray = result.recordset.map(row => ({
      RoleId: row.RoleId,
      RoleName: row.RoleName,
      ModuleId: row.ModuleId,
      ModuleName: row.ModuleName,
      SubModuleId: row.SubModuleId,
      SubModuleName: row.SubModuleName,
      PermissionId: row.PermissionId,
      PermissionName: row.PermissionName,
      SubModulePermissionId: row.SubModulePermissionId,
      HasPermission: row.HasPermission,
      RolePermissionId: row.RolePermissionId
    }));

    console.log(`✅ Matrix processed: ${matrixArray.length} matrix entries`);

    res.json({
      success: true,
      data: matrixArray,
      message: 'Permission matrix retrieved successfully'
    });

  } catch (error) {
    console.error('❌ Error in getPermissionMatrix:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};